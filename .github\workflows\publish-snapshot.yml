name: Publish Snapshots

on:
  workflow_dispatch:
  push:
    branches: [ master ]

jobs:
  publish-core-snapshot:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - uses: gradle/actions/setup-gradle@v4
        with:
          validate-wrappers: true

      - name: Set snapshot version
        run: ./gradlew set-snapshot-version --stacktrace ${{ github.event_name == 'workflow_dispatch' && format('-PsnapshotVersion={0}-SNAPSHOT', github.ref_name) || '' }}

      - name: Publish core package
        id: publish_package
        run: ./gradlew :publishMavenJavaPublicationToGitHubPackagesRepository wiremock-common:publishMavenJavaPublicationToGitHubPackagesRepository wiremock-jetty:publishMavenJavaPublicationToGitHubPackagesRepository --stacktrace

        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
