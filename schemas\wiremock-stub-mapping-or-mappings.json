{"title": "WireMock stub mapping", "type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"absent-pattern": {"title": "Absent matcher", "type": "object", "properties": {"absent": {"type": "boolean"}}, "required": ["absent"]}, "after-pattern": {"title": "After datetime", "type": "object", "properties": {"after": {"$ref": "#/definitions/dateTimeExpression"}, "actualFormat": {"$ref": "#/definitions/format"}, "truncateExpected": {"$ref": "#/definitions/truncation"}, "truncateActual": {"$ref": "#/definitions/truncation"}}, "required": ["after"]}, "and-pattern": {"title": "Logical AND matcher", "type": "object", "properties": {"and": {"type": "array", "items": {"$ref": "#/definitions/content-pattern"}}}, "required": ["and"]}, "bad-request-entity": {"title": "Bad request entity", "type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "integer"}, "source": {"type": "string"}, "title": {"type": "string"}, "detail": {"type": "string"}}}}}}, "base64-string": {"title": "Base64 string", "type": "string", "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$", "description": "A base64 encoded string used to describe binary data."}, "before-pattern": {"title": "Before datetime", "type": "object", "properties": {"before": {"$ref": "#/definitions/dateTimeExpression"}, "actualFormat": {"$ref": "#/definitions/format"}, "truncateExpected": {"$ref": "#/definitions/truncation"}, "truncateActual": {"$ref": "#/definitions/truncation"}}, "required": ["before"]}, "binary-equal-to-pattern": {"title": "Binary equals", "type": "object", "required": ["binaryEqualTo"], "properties": {"binaryEqualTo": {"$ref": "#/definitions/base64-string"}}}, "contains-pattern": {"title": "String contains", "type": "object", "properties": {"contains": {"type": "string"}}, "required": ["contains"]}, "content-pattern": {"type": "object", "title": "Content pattern", "oneOf": [{"$ref": "#/definitions/equal-to-pattern"}, {"$ref": "#/definitions/binary-equal-to-pattern"}, {"$ref": "#/definitions/contains-pattern"}, {"$ref": "#/definitions/does-not-contain-pattern"}, {"$ref": "#/definitions/matches-pattern"}, {"$ref": "#/definitions/does-not-match-pattern"}, {"$ref": "#/definitions/not-pattern"}, {"$ref": "#/definitions/before-pattern"}, {"$ref": "#/definitions/after-pattern"}, {"$ref": "#/definitions/equal-to-date-time-pattern"}, {"$ref": "#/definitions/equal-to-json-pattern"}, {"$ref": "#/definitions/matches-json-path-pattern"}, {"$ref": "#/definitions/equal-to-xml-pattern"}, {"$ref": "#/definitions/matches-xpath-pattern"}, {"$ref": "#/definitions/matches-json-schema-pattern"}, {"$ref": "#/definitions/absent-pattern"}, {"$ref": "#/definitions/and-pattern"}, {"$ref": "#/definitions/or-pattern"}, {"$ref": "#/definitions/has-exactly-multivalue-pattern"}, {"$ref": "#/definitions/includes-multivalue-pattern"}]}, "dateTimeExpression": {"type": "string", "example": "now +3 days"}, "format": {"type": "string", "example": "yyyy-MM-dd"}, "truncation": {"type": "string", "enum": ["first second of minute", "first minute of hour", "first hour of day", "first day of month", "first day of next month", "last day of month", "first day of year", "first day of next year", "last day of year"], "example": "first day of month"}, "delay-distribution": {"type": "object", "description": "The delay distribution. Valid property configuration is either median/sigma/type or lower/type/upper.", "oneOf": [{"title": "Log normal", "description": "Log normal randomly distributed response delay.", "type": "object", "properties": {"median": {"type": "integer"}, "sigma": {"type": "number"}, "maxValue": {"type": "number"}, "type": {"type": "string", "enum": ["lognormal"]}}, "required": ["median", "sigma"]}, {"title": "Uniform", "description": "Uniformly distributed random response delay.", "type": "object", "properties": {"lower": {"type": "integer"}, "upper": {"type": "integer"}, "type": {"type": "string", "enum": ["uniform"]}}, "required": ["lower", "upper"]}, {"title": "Fixed", "description": "Fixed response delay.", "type": "object", "properties": {"milliseconds": {"type": "integer"}, "type": {"type": "string", "enum": ["fixed"]}}, "required": ["milliseconds"]}]}, "does-not-contain-pattern": {"title": "String does not contain", "type": "object", "properties": {"doesNotContain": {"type": "string"}}, "required": ["doesNotContain"]}, "does-not-match-pattern": {"title": "Negative regular expression match", "type": "object", "properties": {"doesNotMatch": {"type": "string", "x-intellij-language-injection": "RegExp"}}, "required": ["doesNotMatch"]}, "equal-to-date-time-pattern": {"title": "Before datetime", "type": "object", "properties": {"equalToDateTime": {"$ref": "#/definitions/dateTimeExpression"}, "actualFormat": {"$ref": "#/definitions/format"}, "truncateExpected": {"$ref": "#/definitions/truncation"}, "truncateActual": {"$ref": "#/definitions/truncation"}}, "required": ["equalToDateTime"]}, "equal-to-json-pattern": {"title": "JSON equals", "type": "object", "properties": {"equalToJson": {"oneOf": [{"type": "object", "description": "The JSON object to match.", "example": {"message": "hello"}}, {"type": "string", "x-intellij-language-injection": "JSON", "description": "A JSON-encoded JSON string to match.", "example": "{ \"message\": \"hello\" }"}]}, "ignoreExtraElements": {"type": "boolean"}, "ignoreArrayOrder": {"type": "boolean"}}, "required": ["equal<PERSON><PERSON><PERSON><PERSON>"]}, "equal-to-pattern": {"title": "String equals", "type": "object", "required": ["equalTo"], "properties": {"equalTo": {"type": "string"}, "caseInsensitive": {"type": "boolean"}}}, "equal-to-xml-pattern": {"title": "XML equality", "type": "object", "properties": {"equalToXml": {"type": "string", "x-intellij-language-injection": "XML", "example": "<amount>123</amount>"}, "enablePlaceholders": {"type": "boolean"}, "placeholderOpeningDelimiterRegex": {"type": "string", "example": "\\["}, "placeholderClosingDelimiterRegex": {"type": "string", "example": "]"}, "namespaceAwareness": {"type": "string", "enum": ["LEGACY", "STRICT", "NONE"]}}, "required": ["equalToXml"]}, "has-exactly-multivalue-pattern": {"title": "Has exactly multi value matcher", "type": "object", "properties": {"hasExactly": {"type": "array", "items": {"$ref": "#/definitions/content-pattern"}}}, "required": ["hasExactly"]}, "headers": {"type": "object", "description": "HTTP headers", "additionalProperties": {"type": "object", "properties": {"key": {"type": "string"}, "values": {"type": "array", "items": {"type": "string"}}}}, "example": {"Connection": {"key": "Connection", "values": ["keep-alive"]}, "Host": {"key": "Host", "values": ["localhost:56738"]}, "User-Agent": {"key": "User-Agent", "values": ["Apache-HttpClient/4.5.1 (Java/1.7.0_51)"]}}}, "health": {"type": "object", "properties": {"status": {"type": "string", "example": "healthy", "description": "The status of the server", "enum": ["healthy", "unhealthy"]}, "message": {"type": "string", "description": "Longer message regarding the status of the server", "example": "Wiremock is ok"}, "version": {"type": "string", "description": "The WireMock version", "example": "3.8.0"}, "uptimeInSeconds": {"type": "integer", "description": "How long the server has been running", "example": 14355}, "timestamp": {"type": "string", "description": "The current timestamp", "example": "2024-07-03T13:16:06.172362Z"}}}, "includes-multivalue-pattern": {"title": "Has exactly multi value matcher", "type": "object", "properties": {"includes": {"type": "array", "items": {"$ref": "#/definitions/content-pattern"}}}, "required": ["includes"]}, "logged-request": {"type": "object", "properties": {"id": {"description": "The unique identifier for this request", "type": "string", "format": "uuid"}, "method": {"description": "The HTTP request method", "type": "string", "example": "GET"}, "url": {"description": "The path and query to match exactly against", "type": "string", "example": "/received-request/2"}, "absoluteUrl": {"description": "The full URL to match against", "type": "string", "example": "http://localhost:56738/received-request/2"}, "scheme": {"description": "The URL scheme (http/https)", "type": "string", "example": "http"}, "host": {"description": "The host part of the URL", "type": "string", "example": "localhost"}, "port": {"description": "The port number", "type": "integer", "example": 56738}, "clientIp": {"description": "The client IP address", "type": "string", "example": "127.0.0.1"}, "headers": {"$ref": "#/definitions/headers"}, "cookies": {"description": "Cookies received with the request", "type": "object", "additionalProperties": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}}, "example": {}}, "body": {"description": "Body string to match against", "type": "string", "example": "Hello world"}, "bodyAsBase64": {"description": "Base64 encoded body content", "type": "string"}, "browserProxyRequest": {"description": "Whether this request was made via a browser proxy", "type": "boolean", "example": false}, "loggedDate": {"description": "The timestamp when the request was logged (epoch millis)", "type": "integer", "format": "int64", "example": 1471442557047}, "loggedDateString": {"description": "The formatted date string when the request was logged", "type": "string", "example": "2016-08-17T14:02:37Z"}, "queryParams": {"description": "Query parameters parsed from the URL", "type": "object", "additionalProperties": {"type": "object", "properties": {"key": {"type": "string"}, "values": {"type": "array", "items": {"type": "string"}}}}}, "formParams": {"description": "Form parameters parsed from the request body", "type": "object", "additionalProperties": {"type": "object", "properties": {"key": {"type": "string"}, "values": {"type": "array", "items": {"type": "string"}}}}}, "multiparts": {"description": "Multipart form data parts", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the multipart part"}, "fileName": {"type": "string", "description": "The filename of the multipart part, if provided"}, "headers": {"$ref": "#/definitions/headers"}, "body": {"type": "string", "description": "The body content of this part"}, "bodyAsBase64": {"type": "string", "description": "Base64 encoded body content of this part"}}, "required": ["name"]}}, "protocol": {"description": "The HTTP protocol version", "type": "string", "example": "HTTP/1.1"}}}, "matches-json-path-pattern": {"title": "JSONPath match", "type": "object", "properties": {"matchesJsonPath": {"oneOf": [{"type": "string", "example": "$.name", "x-intellij-language-injection": "JSONPath"}, {"type": "object", "allOf": [{"properties": {"expression": {"type": "string", "example": "$.name"}}}, {"$ref": "#/definitions/content-pattern"}], "required": ["expression"]}]}}, "required": ["matchesJsonPath"]}, "matches-json-schema-pattern": {"title": "JSON Schema match", "type": "object", "properties": {"matchesJsonSchema": {"type": "string", "x-intellij-language-injection": "JSON", "description": "A valid JSON schema as a string", "example": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"name\"\n  ],\n  \"properties\": {\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"tag\": {\n      \"type\": \"string\"\n    }\n  }\n}"}, "schemaVersion": {"description": "The JSON schema version to interpret the schema against", "example": "V202012", "enum": ["V4", "V6", "V7", "V201909", "V202012"]}}, "required": ["matchesJsonSchema"]}, "matches-pattern": {"title": "Regular expression match", "type": "object", "properties": {"matches": {"type": "string", "x-intellij-language-injection": "RegExp"}}, "required": ["matches"]}, "matches-xpath-pattern": {"title": "XPath match", "type": "object", "properties": {"matchesXPath": {"oneOf": [{"type": "string", "example": "//Order/Amount", "x-intellij-language-injection": "XPath"}, {"type": "object", "allOf": [{"properties": {"expression": {"type": "string", "example": "//Order/Amount"}}}, {"$ref": "#/definitions/content-pattern"}], "required": ["expression"]}]}, "xPathNamespaces": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["matchesXPath"]}, "not-pattern": {"title": "Logical NOT modifier", "type": "object", "properties": {"not": {"$ref": "#/definitions/content-pattern"}}, "required": ["not"]}, "or-pattern": {"title": "Logical AND matcher", "type": "object", "properties": {"or": {"type": "array", "items": {"$ref": "#/definitions/content-pattern"}}}, "required": ["or"]}, "record-spec": {"type": "object", "properties": {"captureHeaders": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"caseInsensitive": {"type": "boolean"}}}, "description": "Headers from the request to include in the generated stub mappings, mapped to parameter objects. The only parameter available is \"caseInsensitive\", which defaults to false", "example": {"Accept": {}, "Content-Type": {"caseInsensitive": true}}}, "extractBodyCriteria": {"type": "object", "description": "Criteria for extracting response bodies to a separate file instead of including it in the stub mapping", "example": [{"binarySizeThreshold": "1 Mb", "textSizeThreshold": "2 kb"}], "properties": {"binarySizeThreshold": {"type": "string", "default": "0", "description": "Size threshold for extracting binary response bodies. Supports humanized size strings, e.g. \"56 Mb\". Default unit is bytes.", "example": "18.2 GB"}, "textSizeThreshold": {"default": "0", "description": "Size threshold for extracting binary response bodies. Supports humanized size strings, e.g. \"56 Mb\". Default unit is bytes.", "example": "18.2 GB", "type": "string"}}}, "persist": {"type": "boolean", "default": true, "description": "Whether to save stub mappings to the file system or just return them"}, "repeatsAsScenarios": {"type": "boolean", "default": true, "description": "When true, duplicate requests will be added to a Scenario. When false, duplicates are discarded"}, "requestBodyPattern": {"type": "object", "description": "Control the request body matcher used in generated stub mappings", "oneOf": [{"type": "object", "description": "Automatically determine matcher based on content type (the default)", "properties": {"caseInsensitive": {"type": "boolean", "default": false, "description": "If equalTo is used, match body use case-insensitive string comparison"}, "ignoreArrayOrder": {"type": "boolean", "default": true, "description": "If equalToJson is used, ignore order of array elements"}, "ignoreExtraElements": {"type": "boolean", "default": true, "description": "If equalToJson is used, matcher ignores extra elements in objects"}, "matcher": {"type": "string", "enum": ["auto"]}}}, {"type": "object", "description": "Always match request bodies using equalTo", "properties": {"caseInsensitive": {"default": false, "description": "Match body using case-insensitive string comparison", "type": "boolean"}, "matcher": {"enum": ["equalTo"], "type": "string"}}}, {"type": "object", "description": "Always match request bodies using equalToJson", "properties": {"ignoreArrayOrder": {"default": true, "description": "Ignore order of array elements", "type": "boolean"}, "ignoreExtraElements": {"default": true, "description": "Ignore extra elements in objects", "type": "boolean"}, "matcher": {"enum": ["equal<PERSON><PERSON><PERSON><PERSON>"], "type": "string"}}}, {"type": "object", "description": "Always match request bodies using equalToXml", "properties": {"matcher": {"type": "string", "enum": ["equalToXml"]}}}]}, "transformerParameters": {"type": "object", "description": "List of names of stub mappings transformers to apply to generated stubs"}, "transformers": {"type": "array", "description": "Parameters to pass to stub mapping transformers", "items": {"type": "string"}}}}, "request-pattern": {"type": "object", "example": "{\n  \"urlPath\" : \"/charges\",\n  \"method\" : \"POST\",\n  \"headers\" : {\n    \"Content-Type\" : {\n      \"equalTo\" : \"application/json\"\n    }\n  }\n}", "properties": {"scheme": {"type": "string", "enum": ["http", "https"], "description": "The scheme (protocol) part of the request URL"}, "host": {"type": "string", "description": "The hostname part of the request URL"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535, "description": "The HTTP port number of the request URL"}, "method": {"type": "string", "pattern": "^[A-Z]+$", "description": "The HTTP request method e.g. GET"}, "url": {"type": "string", "description": "The path and query to match exactly against. Only one of url, urlPattern, urlPath or urlPathPattern may be specified."}, "urlPath": {"type": "string", "description": "The path to match exactly against. Only one of url, urlPattern, urlPath or urlPathPattern may be specified."}, "urlPathPattern": {"type": "string", "description": "The path regex to match against. Only one of url, urlPattern, urlPath or urlPathPattern may be specified."}, "urlPattern": {"type": "string", "description": "The path and query regex to match against. Only one of url, urlPattern, urlPath or urlPathPattern may be specified."}, "urlPathTemplate": {"type": "string", "description": "The path template to match against. Must conform to the OpenAPI compatible subset of the RFC 6570 URI Template specification.\nOnly one of url, urlPattern, urlPath or urlPathPattern may be specified.\n"}, "pathParameters": {"type": "object", "description": "Path parameter patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form. Can only\nbe used when the urlPathPattern URL match type is in use and all keys must be present as variables\nin the path template.", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "queryParameters": {"type": "object", "description": "Query parameter patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "formParameters": {"type": "object", "description": "application/x-www-form-urlencoded form parameter patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "headers": {"type": "object", "description": "Header patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "clientIp": {"type": "string", "description": "The client IP address to match against"}, "basicAuthCredentials": {"type": "object", "description": "Pre-emptive basic auth credentials to match against", "properties": {"password": {"type": "string"}, "username": {"type": "string"}}, "required": ["username", "password"]}, "cookies": {"type": "object", "description": "Cookie patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "bodyPatterns": {"type": "array", "description": "Request body patterns to match against in the { \"<predicate>\": \"<value>\" } form", "items": {"$ref": "#/definitions/content-pattern"}}, "customMatcher": {"type": "object", "description": "Custom request matcher to match against", "properties": {"name": {"type": "string", "description": "The matcher's name specified in the implementation of the matcher."}, "parameters": {"type": "object"}}}, "multipartPatterns": {"type": "array", "description": "Multipart patterns to match against headers and body.", "items": {"type": "object", "properties": {"name": {"type": "string"}, "fileName": {"type": "string"}, "matchingType": {"type": "string", "description": "Determines whether all or any of the parts must match the criteria for an overall match.", "default": "ANY", "enum": ["ALL", "ANY"]}, "headers": {"type": "object", "description": "Header patterns to match against in the <key>: { \"<predicate>\": \"<value>\" } form", "additionalProperties": {"$ref": "#/definitions/content-pattern"}}, "bodyPatterns": {"type": "array", "description": "Body patterns to match against in the { \"<predicate>\": \"<value>\" } form", "items": {"$ref": "#/definitions/content-pattern"}}}}}}}, "response-definition": {"allOf": [{"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status code to be returned"}, "statusMessage": {"type": "string", "description": "The HTTP status message to be returned"}, "headers": {"type": "object", "description": "Map of response headers to send", "additionalProperties": {"type": "string"}}, "additionalProxyRequestHeaders": {"type": "object", "description": "Extra request headers to send when proxying to another host.", "additionalProperties": {"type": "string"}}, "removeProxyRequestHeaders": {"type": "array", "description": "Request headers to remove when proxying to another host.", "items": {"type": "string"}}, "body": {"type": "string", "description": "The response body as a string. Only one of body, base64Body, jsonBody or bodyFileName may be specified."}, "base64Body": {"$ref": "#/definitions/base64-string"}, "jsonBody": {"description": "The response body as a JSON object. Only one of body, base64Body, jsonBody or bodyFileName may be specified.", "oneOf": [{"type": "object"}, {"type": "array"}]}, "bodyFileName": {"type": "string", "description": "The path to the file containing the response body, relative to the configured file root. Only one of body, base64Body, jsonBody or bodyFileName may be specified.", "example": "user-profile-responses/user1.json"}, "fault": {"type": "string", "description": "The fault to apply (instead of a full, valid response).", "enum": ["CONNECTION_RESET_BY_PEER", "EMPTY_RESPONSE", "MALFORMED_RESPONSE_CHUNK", "RANDOM_DATA_THEN_CLOSE"]}, "fixedDelayMilliseconds": {"type": "integer", "description": "Number of milliseconds to delay be before sending the response."}, "delayDistribution": {"$ref": "#/definitions/delay-distribution"}, "chunkedDribbleDelay": {"type": "object", "description": "The parameters for chunked dribble delay - chopping the response into pieces and sending them at delayed intervals", "properties": {"numberOfChunks": {"type": "integer"}, "totalDuration": {"type": "integer"}}, "required": ["numberOfChunks", "totalDuration"]}, "fromConfiguredStub": {"type": "boolean", "description": "Read-only flag indicating false if this was the default, unmatched response. Not present otherwise."}, "proxyBaseUrl": {"type": "string", "description": "The base URL of the target to proxy matching requests to."}, "proxyUrlPrefixToRemove": {"type": "string", "description": "A path segment to remove from the beginning in incoming request URL paths before proxying to the target."}, "transformerParameters": {"type": "object", "description": "Parameters to apply to response transformers."}, "transformers": {"type": "array", "description": "List of names of transformers to apply to this response.", "items": {"type": "string"}}}}]}, "scenario": {"type": "object", "properties": {"id": {"type": "string", "description": "The scenario ID", "example": "c8d249ec-d86d-48b1-88a8-a660e6848042"}, "name": {"type": "string", "description": "The scenario name", "example": "my_scenario"}, "possibleStates": {"type": "array", "items": {"default": "Started", "description": "All the states this scenario can be in", "type": "string"}, "example": ["Started", "state_1", "state_2"]}, "state": {"type": "string", "default": "Started", "description": "The current state of this scenario", "example": "state_2"}}}, "stub-mapping": {"type": "object", "properties": {"id": {"type": "string", "description": "This stub mapping's unique identifier"}, "uuid": {"type": "string", "description": "Alias for the id"}, "name": {"type": "string", "description": "The stub mapping's name"}, "request": {"$ref": "#/definitions/request-pattern"}, "response": {"$ref": "#/definitions/response-definition"}, "persistent": {"type": "boolean", "description": "Indicates that the stub mapping should be persisted immediately on create/update/delete and survive resets to default."}, "priority": {"type": "integer", "description": "This stub mapping's priority relative to others. 1 is highest.", "minimum": 1}, "scenarioName": {"type": "string", "description": "The name of the scenario that this stub mapping is part of"}, "requiredScenarioState": {"type": "string", "description": "The required state of the scenario in order for this stub to be matched."}, "newScenarioState": {"type": "string", "description": "The new state for the scenario to be updated to after this stub is served."}, "postServeActions": {"type": "array", "description": "DEPRECATED: Use `serveEventListeners` instead.\nA map of the names of post serve action extensions to trigger and their parameters.\n", "items": {"type": "object"}}, "serveEventListeners": {"type": "array", "description": "The list of serve event listeners", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestPhases": {"type": "array", "items": {"type": "string", "enum": ["BEFORE_MATCH", "AFTER_MATCH", "BEFORE_RESPONSE_SENT", "AFTER_COMPLETE"]}}, "parameters": {"type": "object"}}}}, "metadata": {"type": "object", "description": "Arbitrary metadata to be used for e.g. tagging, documentation. Can also be used to find and remove stubs."}}, "additionalProperties": false}, "stub-mappings": {"type": "object", "properties": {"mappings": {"type": "array", "items": {"$ref": "#/definitions/stub-mapping"}}, "meta": {"type": "object", "properties": {"total": {"type": "integer", "example": 4}}, "required": ["total"]}}, "additionalProperties": false}}, "oneOf": [{"$ref": "#/definitions/stub-mapping"}, {"$ref": "#/definitions/stub-mappings"}]}