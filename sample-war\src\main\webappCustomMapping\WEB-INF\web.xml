<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_9" version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee
  http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">

    <!-- This is a way of integrating the wiremock servlet and mounting it under a path which is different
     from the root path. For reference please use the version in the webapp folder. This version is stripped
     down. -->
  
  <listener>
  	<display-name>wiremock-startup-listener</display-name>
  	<listener-class>com.github.tomakehurst.wiremock.servlet.WireMockWebContextListener</listener-class>
  </listener>
  
  <context-param>
  	<param-name>WireMockFileSourceRoot</param-name>
  	<param-value>/WEB-INF/wiremock</param-value>
  </context-param>

  <context-param>
    <param-name>verboseLoggingEnabled</param-name>
    <param-value>false</param-value>
  </context-param>


  <servlet>
  	<servlet-name>wiremock-mock-service-handler-servlet</servlet-name>
  	<servlet-class>com.github.tomakehurst.wiremock.servlet.WireMockHandlerDispatchingServlet</servlet-class>
  	<init-param>
  		<param-name>RequestHandlerClass</param-name>
  		<param-value>com.github.tomakehurst.wiremock.http.StubRequestHandler</param-value>
  	</init-param>
    <init-param>
     <!-- A servlet mapping path may be specified to allow a mapping of wiremock to a different path.
     This has to be equal to the servlet mappings (e.g. if the wiremock servlet is mapped under /mapping and
     the wiremock admin servlet is mapped under /mapping/__admin then the correct value for this setting would be
     /mapping) -->
      <param-name>mappedUnder</param-name>
      <param-value>/mapping</param-value>
    </init-param>
  </servlet>
  <servlet-mapping>
  	<servlet-name>wiremock-mock-service-handler-servlet</servlet-name>
  	<url-pattern>/mapping/*</url-pattern>
  </servlet-mapping>
  
  <servlet>
  	<servlet-name>wiremock-admin-handler-servlet</servlet-name>
  	<servlet-class>com.github.tomakehurst.wiremock.servlet.WireMockHandlerDispatchingServlet</servlet-class>
  	<init-param>
  		<param-name>RequestHandlerClass</param-name>
  		<param-value>com.github.tomakehurst.wiremock.http.AdminRequestHandler</param-value>
  	</init-param>
    <init-param>
      <!-- A servlet mapping path may be specified to allow a mapping of wiremock to a different path.
      This has to be equal to the servlet mappings (e.g. if the wiremock servlet is mapped under /mapping and
      the wiremock admin servlet is mapped under /mapping/__admin then the correct value for this setting would be
      /mapping) -->
      <param-name>mappedUnder</param-name>
      <param-value>/mapping/__admin</param-value>
    </init-param>
  </servlet>
  <servlet-mapping>
  	<servlet-name>wiremock-admin-handler-servlet</servlet-name>
  	<url-pattern>/mapping/__admin/*</url-pattern>
  </servlet-mapping>
</web-app>