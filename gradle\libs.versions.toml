[versions]
jetty = "12.0.20"
jackson = "2.20.0"
xmlUnit = "2.10.3"
jsonUnit = "2.40.1"
junitJupiter = "5.13.4"
handlebars = "4.5.0"
slf4j = "1.7.36"
hamcrest = "3.0"
mockito = "5.19.0"
jmh = "1.37"
apache-http5 = "5.4.3"
archunit = "1.4.1"
vanniktech-maven-publish = "0.34.0"

[libraries]
# Jetty dependencies
jetty-bom = { module = "org.eclipse.jetty:jetty-bom", version.ref = "jetty" }
jetty-ee10-bom = { module = "org.eclipse.jetty.ee10:jetty-ee10-bom", version.ref = "jetty" }
jetty-server = { module = "org.eclipse.jetty:jetty-server" }
jetty-proxy = { module = "org.eclipse.jetty:jetty-proxy" }
jetty-http2-server = { module = "org.eclipse.jetty.http2:jetty-http2-server" }
jetty-alpn-server = { module = "org.eclipse.jetty:jetty-alpn-server" }
jetty-alpn-java-server = { module = "org.eclipse.jetty:jetty-alpn-java-server" }
jetty-alpn-java-client = { module = "org.eclipse.jetty:jetty-alpn-java-client" }
jetty-alpn-client = { module = "org.eclipse.jetty:jetty-alpn-client" }
jetty-ee10-servlet = { module = "org.eclipse.jetty.ee10:jetty-ee10-servlet" }
jetty-ee10-servlets = { module = "org.eclipse.jetty.ee10:jetty-ee10-servlets" }
jetty-ee10-webapp = { module = "org.eclipse.jetty.ee10:jetty-ee10-webapp" }
jetty-client = { module = "org.eclipse.jetty:jetty-client" }
jetty-http2-client = { module = "org.eclipse.jetty.http2:jetty-http2-client" }
jetty-http2-client-transport = { module = "org.eclipse.jetty.http2:jetty-http2-client-transport" }
jetty-http2-common = { module = "org.eclipse.jetty.http2:jetty-http2-common" }
jetty-http = { module = "org.eclipse.jetty:jetty-http" }
jetty-io = { module = "org.eclipse.jetty:jetty-io" }
jetty-util = { module = "org.eclipse.jetty:jetty-util" }

# Jackson dependencies
jackson-bom = { module = "com.fasterxml.jackson:jackson-bom", version.ref = "jackson" }
jackson-core = { module = "com.fasterxml.jackson.core:jackson-core" }
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind" }
jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" }

# Guava
guava = { module = "com.google.guava:guava", version = "33.4.8-jre" }

# HTTP Client
apache-http5-client = { module = "org.apache.httpcomponents.client5:httpclient5", version = "5.5" }
apache-http5-core = { module = "org.apache.httpcomponents.core5:httpcore5", version = "5.3.5" }

# XML Unit
xmlunit-core = { module = "org.xmlunit:xmlunit-core", version.ref = "xmlUnit" }
xmlunit-legacy = { module = "org.xmlunit:xmlunit-legacy", version.ref = "xmlUnit" }
xmlunit-placeholders = { module = "org.xmlunit:xmlunit-placeholders", version.ref = "xmlUnit" }

# JSON Unit
json-unit-core = { module = "net.javacrumbs.json-unit:json-unit-core", version.ref = "jsonUnit" }
json-unit = { module = "net.javacrumbs.json-unit:json-unit", version.ref = "jsonUnit" }

# JSON Path
json-path = { module = "com.jayway.jsonpath:json-path", version = "2.9.0" }
json-smart = { module = "net.minidev:json-smart", version = "2.5.2" }

# SLF4J
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
slf4j-nop = { module = "org.slf4j:slf4j-nop", version.ref = "slf4j" }

# Command line options
jopt-simple = { module = "net.sf.jopt-simple:jopt-simple", version = "5.0.4" }

# JUnit
junit4 = { module = "junit:junit", version = "4.13.2" }
junit-bom = { module = "org.junit:junit-bom", version.ref = "junitJupiter" }
junit-jupiter = { module = "org.junit.jupiter:junit-jupiter" }
junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api" }
junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params" }
junit-platform-testkit = { module = "org.junit.platform:junit-platform-testkit" }
junit-platform-launcher = { module = "org.junit.platform:junit-platform-launcher" }
junit-platform-engine = { module = "org.junit.platform:junit-platform-engine" }
junit-platform-commons = { module = "org.junit.platform:junit-platform-commons" }
junit-vintage-engine = { module = "org.junit.vintage:junit-vintage-engine" }
junit-pioneer = { module = "org.junit-pioneer:junit-pioneer", version = "2.3.0" }

# Handlebars
handlebars = { module = "com.github.jknack:handlebars", version.ref = "handlebars" }
handlebars-helpers = { module = "com.github.jknack:handlebars-helpers", version.ref = "handlebars" }

# File upload
commons-fileupload = { module = "commons-fileupload:commons-fileupload", version = "1.6.0" }

# JSON Schema
json-schema-validator = { module = "com.networknt:json-schema-validator", version = "1.5.8" }

# Testing
hamcrest = { module = "org.hamcrest:hamcrest", version.ref = "hamcrest" }
hamcrest-core = { module = "org.hamcrest:hamcrest-core", version.ref = "hamcrest" }
hamcrest-library = { module = "org.hamcrest:hamcrest-library", version.ref = "hamcrest" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito" }
mockito-junit-jupiter = { module = "org.mockito:mockito-junit-jupiter", version.ref = "mockito" }
jsonassert = { module = "org.skyscreamer:jsonassert", version = "1.5.1" }
jsonassert-toomuchcoding = { module = "com.toomuchcoding.jsonassert:jsonassert", version = "0.8.0" }
awaitility = { module = "org.awaitility:awaitility", version = "4.3.0" }
commons-io = { module = "commons-io:commons-io", version = "2.20.0" }
scala-library = { module = "org.scala-lang:scala-library", version = "2.13.16" }
archunit = { module = "com.tngtech.archunit:archunit", version.ref = "archunit" }
archunit-junit5 = { module = "com.tngtech.archunit:archunit-junit5", version.ref = "archunit" }
archunit-junit5-api = { module = "com.tngtech.archunit:archunit-junit5-api", version.ref = "archunit" }
android-json = { module = "com.vaadin.external.google:android-json", version = "0.0.20131108.vaadin1" }
assertj-core = { module = "org.assertj:assertj-core", version = "3.27.4" }

# JMH
jmh-core = { module = "org.openjdk.jmh:jmh-core", version.ref = "jmh" }
jmh-generator-annprocess = { module = "org.openjdk.jmh:jmh-generator-annprocess", version.ref = "jmh" }

jakarta-servlet-api = { module = "jakarta.servlet:jakarta.servlet-api", version = "6.1.0" }

[plugins]
nexus-publish = { id = "io.github.gradle-nexus.publish-plugin", version = "2.0.0" }
vanniktech-maven-publish = { id = "com.vanniktech.maven.publish", version.ref = "vanniktech-maven-publish" }
vanniktech-maven-publish-base = { id = "com.vanniktech.maven.publish.base", version.ref = "vanniktech-maven-publish" }
spotless = { id = "com.diffplug.spotless", version = "6.25.0" }
shadow = { id = "com.github.johnrengelman.shadow", version = "8.1.1" }
sonarqube = { id = "org.sonarqube", version = "6.3.1.5724" }
jmh = { id = "me.champeau.jmh", version = "0.7.3" }
task-tree = { id = "com.dorongold.task-tree", version = "4.0.1" }
