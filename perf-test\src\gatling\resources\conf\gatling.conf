gatling {
  http {
    enableGA = false

    ahc {
      connectTimeout = 5000                               # Timeout when establishing a connection
      handshakeTimeout = 5000                             # Timeout when performing TLS hashshake
      pooledConnectionIdleTimeout = 30000                 # Timeout when a connection stays unused in the pool
      readTimeout = 10000                                 # Timeout when a used connection stays idle
      maxRetry = 0                                        # Number of times that a request should be tried again
      requestTimeout = 10000                              # Timeout of the requests
    }
  }
}
