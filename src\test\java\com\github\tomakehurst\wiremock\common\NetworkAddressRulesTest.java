/*
 * Copyright (C) 2022-2023 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.github.tomakehurst.wiremock.common;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import org.junit.jupiter.api.Test;

public class NetworkAddressRulesTest {

  @Test
  void allowsAddressIncludedAndNotExcluded() {
    NetworkAddressRules rules =
        NetworkAddressRules.builder()
            .allow("********-********")
            .allow("***********-***********")
            .deny("********")
            .deny("********")
            .build();

    assertThat(rules.isAllowed("***********11"), is(true));

    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
  }

  @Test
  void onlyAllowSingleIp() {
    NetworkAddressRules rules = NetworkAddressRules.builder().allow("********").build();

    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
  }

  @Test
  void onlyDenySingleIp() {
    NetworkAddressRules rules = NetworkAddressRules.builder().deny("********").build();

    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(true));
  }

  @Test
  void allowAndDenySingleIps() {
    NetworkAddressRules rules =
        NetworkAddressRules.builder().deny("********").allow("********").build();

    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(false));
  }

  @Test
  void allowRangeAndDenySingleIp() {
    NetworkAddressRules rules =
        NetworkAddressRules.builder().allow("********-********").deny("********").build();

    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(true));
    assertThat(rules.isAllowed("********"), is(false));
  }

  @Test
  void denyRangeAndAllowSingleIp() {
    NetworkAddressRules rules =
        NetworkAddressRules.builder().deny("********-********").allow("********").build();

    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
    assertThat(rules.isAllowed("********"), is(false));
  }
}
