# This workflow will build a Java project with <PERSON><PERSON><PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-gradle

name: Build and test

on:
  push:
    branches: [ master, v4.x ]
  pull_request:
    branches: [ master, v4.x ]
  workflow_dispatch:

jobs:
  core:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        jdk: [17]
    runs-on: ${{ matrix.os }}
    env:
      JDK_VERSION:  ${{ matrix.jdk }}

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ matrix.jdk }}
        distribution: 'temurin'
    - name: Setup Gradle
      uses: gradle/actions/setup-gradle@v4
      with:
        validate-wrappers: true
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    
    - name: Cache SonarCloud packages
      uses: actions/cache@v4
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar

    - name: Test WireMock with Sonarqube
      if: ${{ matrix.os == 'ubuntu-latest' && matrix.jdk == 17 && github.event_name == 'push' }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed to get PR information, if any
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: ./gradlew :check :sonarqube --stacktrace --no-daemon

    - name: Test WireMock
      run: ./gradlew :check --stacktrace --no-daemon

    - name: Archive WireMock reports - ${{ matrix.os }} JDK ${{ matrix.jdk }}
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: wiremock-core-test-report-${{ matrix.os }} JDK ${{ matrix.jdk }}
        path: |
          build/reports/tests/test
          build/test-results/test
          build/reports/dependency-analysis/build-health-report.txt
