/*
 * Copyright (C) 2023 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.github.tomakehurst.wiremock.common;

import static com.github.tomakehurst.wiremock.common.Lazy.lazy;
import static org.assertj.core.api.Assertions.assertThat;

import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.Test;

public class LazyTest {

  @Test
  void initialisesFromSupplierOnlyOnce() {
    AtomicInteger count = new AtomicInteger(0);

    Lazy<String> lazy =
        lazy(
            () -> {
              count.incrementAndGet();
              return "Lazily";
            });

    lazy.get();
    lazy.get();

    assertThat(lazy.get()).isEqualTo("Lazily");
    assertThat(count.get()).isEqualTo(1);
  }
}
