#
# This is copied from joptsimple.
# Properties prefixed with "wiremock" to match after relocation into standalone jar
# Without this, the "--help" command will throw MissingResourceException
#
wiremock.joptsimple.BuiltinHelpFormatter.no.options.specified = No options specified
wiremock.joptsimple.BuiltinHelpFormatter.non.option.arguments.header = Non-option arguments:
wiremock.joptsimple.BuiltinHelpFormatter.option.header.with.required.indicator = Option (* = required)
wiremock.joptsimple.BuiltinHelpFormatter.option.divider.with.required.indicator = ---------------------
wiremock.joptsimple.BuiltinHelpFormatter.option.header = Option
wiremock.joptsimple.BuiltinHelpFormatter.option.divider = ------
wiremock.joptsimple.BuiltinHelpFormatter.description.header = Description
wiremock.joptsimple.BuiltinHelpFormatter.description.divider = -----------
wiremock.joptsimple.BuiltinHelpFormatter.default.value.header = default:
wiremock.joptsimple.AlternativeLongOptionSpec.description = Alternative form of long options
wiremock.joptsimple.AlternativeLongOptionSpec.arg.description = opt=value
